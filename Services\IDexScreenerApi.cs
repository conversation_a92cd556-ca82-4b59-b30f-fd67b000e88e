using Refit;
using TokenBot.Models;

namespace TokenBot.Services;

/// <summary>
/// DexScreener API client interface
/// Base URL: https://api.dexscreener.com
/// </summary>
public interface IDexScreenerApi
{
    /// <summary>
    /// Get the latest token profiles (rate-limit 60 requests per minute)
    /// </summary>
    [Get("/token-profiles/latest/v1")]
    Task<List<TokenProfile>> GetLatestTokenProfilesAsync();

    /// <summary>
    /// Get the latest boosted tokens (rate-limit 60 requests per minute)
    /// </summary>
    [Get("/token-boosts/latest/v1")]
    Task<List<TokenBoost>> GetLatestTokenBoostsAsync();

    /// <summary>
    /// Get the tokens with most active boosts (rate-limit 60 requests per minute)
    /// </summary>
    [Get("/token-boosts/top/v1")]
    Task<List<TokenBoost>> GetTopTokenBoostsAsync();

    /// <summary>
    /// Check orders paid for of token (rate-limit 60 requests per minute)
    /// </summary>
    /// <param name="chainId">Chain ID (e.g., "solana")</param>
    /// <param name="tokenAddress">Token address</param>
    [Get("/orders/v1/{chainId}/{tokenAddress}")]
    Task<List<Order>> GetOrdersAsync(string chainId, string tokenAddress);

    /// <summary>
    /// Get one or multiple pairs by chain and pair address (rate-limit 300 requests per minute)
    /// </summary>
    /// <param name="chainId">Chain ID (e.g., "solana")</param>
    /// <param name="pairId">Pair address</param>
    [Get("/latest/dex/pairs/{chainId}/{pairId}")]
    Task<PairsResponse> GetPairAsync(string chainId, string pairId);

    /// <summary>
    /// Search for pairs matching query (rate-limit 300 requests per minute)
    /// </summary>
    /// <param name="query">Search query (e.g., "SOL/USDC")</param>
    [Get("/latest/dex/search")]
    Task<PairsResponse> SearchPairsAsync([Query] string q);

    /// <summary>
    /// Get the pools of a given token address (rate-limit 300 requests per minute)
    /// </summary>
    /// <param name="chainId">Chain ID (e.g., "solana")</param>
    /// <param name="tokenAddress">Token address</param>
    [Get("/token-pairs/v1/{chainId}/{tokenAddress}")]
    Task<List<Pair>> GetTokenPairsAsync(string chainId, string tokenAddress);

    /// <summary>
    /// Get one or multiple pairs by token address (rate-limit 300 requests per minute)
    /// </summary>
    /// <param name="chainId">Chain ID (e.g., "solana")</param>
    /// <param name="tokenAddresses">One or multiple, comma-separated token addresses (up to 30 addresses)</param>
    [Get("/tokens/v1/{chainId}/{tokenAddresses}")]
    Task<List<Pair>> GetTokensAsync(string chainId, string tokenAddresses);
}
