using Refit;
using TokenBot.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add DexScreener API client
builder.Services.AddRefitClient<IDexScreenerApi>()
    .ConfigureHttpClient(c =>
    {
        c.BaseAddress = new Uri("https://api.dexscreener.com");
        c.DefaultRequestHeaders.Add("User-Agent", "TokenBot/1.0");
    });

// Add DexScreener service
builder.Services.AddScoped<DexScreenerService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.MapControllers();

app.Run();
