using TokenBot.Models;

namespace TokenBot.Services;

/// <summary>
/// Service wrapper for DexScreener API with additional business logic
/// </summary>
public class DexScreenerService
{
    private readonly IDexScreenerApi _api;
    private readonly ILogger<DexScreenerService> _logger;

    public DexScreenerService(IDexScreenerApi api, ILogger<DexScreenerService> logger)
    {
        _api = api;
        _logger = logger;
    }

    /// <summary>
    /// Get the latest token profiles
    /// </summary>
    public async Task<List<TokenProfile>> GetLatestTokenProfilesAsync()
    {
        try
        {
            _logger.LogInformation("Fetching latest token profiles from DexScreener");
            var profiles = await _api.GetLatestTokenProfilesAsync();
            _logger.LogInformation("Successfully fetched {Count} token profiles", profiles?.Count ?? 0);
            return profiles ?? new List<TokenProfile>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching latest token profiles");
            throw;
        }
    }

    /// <summary>
    /// Get the latest boosted tokens
    /// </summary>
    public async Task<List<TokenBoost>> GetLatestTokenBoostsAsync()
    {
        try
        {
            _logger.LogInformation("Fetching latest token boosts from DexScreener");
            var boosts = await _api.GetLatestTokenBoostsAsync();
            _logger.LogInformation("Successfully fetched {Count} token boosts", boosts?.Count ?? 0);
            return boosts ?? new List<TokenBoost>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching latest token boosts");
            throw;
        }
    }

    /// <summary>
    /// Get the tokens with most active boosts
    /// </summary>
    public async Task<List<TokenBoost>> GetTopTokenBoostsAsync()
    {
        try
        {
            _logger.LogInformation("Fetching top token boosts from DexScreener");
            var boosts = await _api.GetTopTokenBoostsAsync();
            _logger.LogInformation("Successfully fetched {Count} top token boosts", boosts?.Count ?? 0);
            return boosts ?? new List<TokenBoost>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching top token boosts");
            throw;
        }
    }

    /// <summary>
    /// Check orders paid for a specific token
    /// </summary>
    public async Task<List<Order>> GetOrdersAsync(string chainId, string tokenAddress)
    {
        try
        {
            _logger.LogInformation("Fetching orders for token {TokenAddress} on chain {ChainId}", tokenAddress, chainId);
            var orders = await _api.GetOrdersAsync(chainId, tokenAddress);
            _logger.LogInformation("Successfully fetched {Count} orders", orders?.Count ?? 0);
            return orders ?? new List<Order>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching orders for token {TokenAddress} on chain {ChainId}", tokenAddress, chainId);
            throw;
        }
    }

    /// <summary>
    /// Get pair information by chain and pair address
    /// </summary>
    public async Task<PairsResponse?> GetPairAsync(string chainId, string pairId)
    {
        try
        {
            _logger.LogInformation("Fetching pair {PairId} on chain {ChainId}", pairId, chainId);
            var response = await _api.GetPairAsync(chainId, pairId);
            _logger.LogInformation("Successfully fetched pair data with {Count} pairs", response?.Pairs?.Count ?? 0);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching pair {PairId} on chain {ChainId}", pairId, chainId);
            throw;
        }
    }

    /// <summary>
    /// Search for pairs matching a query
    /// </summary>
    public async Task<PairsResponse?> SearchPairsAsync(string query)
    {
        try
        {
            _logger.LogInformation("Searching pairs with query: {Query}", query);
            var response = await _api.SearchPairsAsync(query);
            _logger.LogInformation("Successfully found {Count} pairs for query: {Query}", response?.Pairs?.Count ?? 0, query);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching pairs with query: {Query}", query);
            throw;
        }
    }

    /// <summary>
    /// Get all pairs for a specific token
    /// </summary>
    public async Task<List<Pair>> GetTokenPairsAsync(string chainId, string tokenAddress)
    {
        try
        {
            _logger.LogInformation("Fetching token pairs for {TokenAddress} on chain {ChainId}", tokenAddress, chainId);
            var pairs = await _api.GetTokenPairsAsync(chainId, tokenAddress);
            _logger.LogInformation("Successfully fetched {Count} token pairs", pairs?.Count ?? 0);
            return pairs ?? new List<Pair>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching token pairs for {TokenAddress} on chain {ChainId}", tokenAddress, chainId);
            throw;
        }
    }

    /// <summary>
    /// Get pairs by multiple token addresses
    /// </summary>
    public async Task<List<Pair>> GetTokensAsync(string chainId, params string[] tokenAddresses)
    {
        if (tokenAddresses.Length > 30)
        {
            throw new ArgumentException("Maximum 30 token addresses allowed", nameof(tokenAddresses));
        }

        var tokenAddressesString = string.Join(",", tokenAddresses);
        
        try
        {
            _logger.LogInformation("Fetching tokens for {Count} addresses on chain {ChainId}", tokenAddresses.Length, chainId);
            var pairs = await _api.GetTokensAsync(chainId, tokenAddressesString);
            _logger.LogInformation("Successfully fetched {Count} token pairs", pairs?.Count ?? 0);
            return pairs ?? new List<Pair>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching tokens for {Count} addresses on chain {ChainId}", tokenAddresses.Length, chainId);
            throw;
        }
    }
}
