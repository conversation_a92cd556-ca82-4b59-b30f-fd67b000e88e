# TokenBot - DexScreener API Client

This project implements a Refit-based client for the DexScreener API, providing easy access to cryptocurrency trading pair data, token information, and market analytics.

## Features

- **Complete DexScreener API Coverage**: All endpoints from the official API documentation
- **Refit Integration**: Type-safe HTTP client with automatic serialization/deserialization
- **Dependency Injection**: Properly configured for ASP.NET Core DI container
- **Logging**: Comprehensive logging for monitoring and debugging
- **Error Handling**: Robust error handling with proper HTTP status codes
- **Rate Limiting Awareness**: Documented rate limits for each endpoint

## API Endpoints

### DexScreener API Endpoints

The client provides access to all DexScreener API endpoints:

#### Token Information
- `GET /api/dexscreener/token-profiles/latest` - Latest token profiles
- `GET /api/dexscreener/token-boosts/latest` - Latest boosted tokens  
- `GET /api/dexscreener/token-boosts/top` - Top boosted tokens

#### Trading Pairs
- `GET /api/dexscreener/pairs/{chainId}/{pairId}` - Get specific pair
- `GET /api/dexscreener/search?query={query}` - Search pairs
- `GET /api/dexscreener/token-pairs/{chainId}/{tokenAddress}` - Get token pairs
- `GET /api/dexscreener/tokens/{chainId}/{tokenAddresses}` - Get multiple tokens

#### Orders
- `GET /api/dexscreener/orders/{chainId}/{tokenAddress}` - Check token orders

### Example Endpoints

The project includes example endpoints demonstrating practical usage:

- `GET /api/example/solana-search?query=SOL` - Search Solana tokens
- `GET /api/example/popular-solana-tokens` - Get popular Solana token info
- `GET /api/example/trending` - Get trending/boosted tokens

## Usage Examples

### Basic Usage

```csharp
// Inject the service in your controller
public class MyController : ControllerBase
{
    private readonly DexScreenerService _dexScreenerService;
    
    public MyController(DexScreenerService dexScreenerService)
    {
        _dexScreenerService = dexScreenerService;
    }
    
    // Search for pairs
    public async Task<IActionResult> SearchTokens(string query)
    {
        var results = await _dexScreenerService.SearchPairsAsync(query);
        return Ok(results);
    }
}
```

### Direct API Client Usage

```csharp
// Inject the Refit client directly
public class MyService
{
    private readonly IDexScreenerApi _api;
    
    public MyService(IDexScreenerApi api)
    {
        _api = api;
    }
    
    public async Task<List<Pair>> GetTokenPairs(string chainId, string tokenAddress)
    {
        return await _api.GetTokenPairsAsync(chainId, tokenAddress);
    }
}
```

## Configuration

The DexScreener client is configured in `Program.cs`:

```csharp
// Add DexScreener API client
builder.Services.AddRefitClient<IDexScreenerApi>()
    .ConfigureHttpClient(c =>
    {
        c.BaseAddress = new Uri("https://api.dexscreener.com");
        c.DefaultRequestHeaders.Add("User-Agent", "TokenBot/1.0");
    });

// Add DexScreener service
builder.Services.AddScoped<DexScreenerService>();
```

## Rate Limits

DexScreener API has the following rate limits:
- **Token profiles/boosts/orders**: 60 requests per minute
- **Pairs/search/tokens**: 300 requests per minute

## Data Models

The project includes comprehensive data models for all API responses:

- `TokenProfile` - Token profile information
- `TokenBoost` - Token boost data
- `Pair` - Trading pair information
- `Token` - Basic token data
- `Order` - Order information
- `PairsResponse` - Search/pair response wrapper

## Error Handling

All endpoints include proper error handling:
- Logging of errors with context
- HTTP 500 responses for server errors
- HTTP 400 responses for bad requests
- Detailed error messages in development

## Getting Started

1. **Clone the repository**
2. **Run the application**:
   ```bash
   dotnet run
   ```
3. **Open Swagger UI**: Navigate to `https://localhost:7xxx/swagger`
4. **Test the endpoints**: Use the Swagger interface to test API calls

## Example API Calls

### Search for SOL tokens
```
GET /api/example/solana-search?query=SOL
```

### Get specific pair information
```
GET /api/dexscreener/pairs/solana/JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN
```

### Get token pairs for a specific token
```
GET /api/dexscreener/token-pairs/solana/So11111111111111111111111111111111111111112
```

## Dependencies

- **Refit**: HTTP client library
- **Refit.HttpClientFactory**: Integration with ASP.NET Core DI
- **ASP.NET Core**: Web framework
- **System.Text.Json**: JSON serialization

## Contributing

Feel free to submit issues and enhancement requests!
