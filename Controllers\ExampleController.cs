using Microsoft.AspNetCore.Mvc;
using TokenBot.Services;

namespace TokenBot.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ExampleController : ControllerBase
{
    private readonly DexScreenerService _dexScreenerService;
    private readonly ILogger<ExampleController> _logger;

    public ExampleController(DexScreenerService dexScreenerService, ILogger<ExampleController> logger)
    {
        _dexScreenerService = dexScreenerService;
        _logger = logger;
    }

    /// <summary>
    /// Example endpoint showing how to search for Solana tokens
    /// </summary>
    [HttpGet("solana-search")]
    public async Task<IActionResult> SearchSolanaTokens([FromQuery] string query = "SOL")
    {
        try
        {
            _logger.LogInformation("Searching for Solana tokens with query: {Query}", query);

            var searchResults = await _dexScreenerService.SearchPairsAsync(query);

            if (searchResults?.Pairs == null || !searchResults.Pairs.Any())
            {
                return Ok(new { message = "No pairs found", query });
            }

            // Filter for Solana pairs only
            var solanaPairs = searchResults.Pairs
                .Where(p => p.ChainId?.ToLower() == "solana")
                .Take(5) // Limit to first 5 results
                .Select(p => new
                {
                    PairAddress = p.PairAddress,
                    BaseToken = new
                    {
                        p.BaseToken?.Symbol,
                        p.BaseToken?.Name,
                        p.BaseToken?.Address
                    },
                    QuoteToken = new
                    {
                        p.QuoteToken?.Symbol,
                        p.QuoteToken?.Name,
                        p.QuoteToken?.Address
                    },
                    PriceUsd = p.PriceUsd,
                    MarketCap = p.MarketCap,
                    Liquidity = p.Liquidity?.Usd,
                    Volume24h = p.Volume?.ContainsKey("h24") == true ? (decimal?)p.Volume["h24"] : null,
                    PriceChange24h = p.PriceChange?.ContainsKey("h24") == true ? (decimal?)p.PriceChange["h24"] : null,
                    Url = p.Url
                })
                .ToList();

            return Ok(new
            {
                query,
                totalFound = searchResults.Pairs.Count,
                solanaResults = solanaPairs.Count(),
                pairs = solanaPairs
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching for Solana tokens");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Example endpoint showing how to get token information for popular Solana tokens
    /// </summary>
    [HttpGet("popular-solana-tokens")]
    public async Task<IActionResult> GetPopularSolanaTokens()
    {
        try
        {
            // Popular Solana token addresses
            var popularTokens = new[]
            {
                "So11111111111111111111111111111111111111112", // SOL
                "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", // USDC
                "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"  // USDT
            };

            _logger.LogInformation("Fetching information for {Count} popular Solana tokens", popularTokens.Length);

            var tokenPairs = await _dexScreenerService.GetTokensAsync("solana", popularTokens);

            if (!tokenPairs.Any())
            {
                return Ok(new { message = "No token pairs found" });
            }

            var tokenInfo = tokenPairs
                .GroupBy(p => p.BaseToken?.Address)
                .Select(g => new
                {
                    TokenAddress = g.Key,
                    Symbol = g.First().BaseToken?.Symbol,
                    Name = g.First().BaseToken?.Name,
                    PairsCount = g.Count(),
                    BestPair = g.OrderByDescending(p => p.Liquidity?.Usd ?? 0).First(),
                    TotalLiquidity = g.Sum(p => p.Liquidity?.Usd ?? 0),
                    HighestPrice = g.Max(p => decimal.TryParse(p.PriceUsd, out var price) ? price : 0)
                })
                .ToList();

            return Ok(new
            {
                message = "Popular Solana tokens information",
                tokensCount = tokenInfo.Count,
                tokens = tokenInfo
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching popular Solana tokens");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }

    /// <summary>
    /// Example endpoint showing how to get latest boosted tokens
    /// </summary>
    [HttpGet("trending")]
    public async Task<IActionResult> GetTrendingTokens()
    {
        try
        {
            _logger.LogInformation("Fetching trending (boosted) tokens");

            var latestBoosts = await _dexScreenerService.GetLatestTokenBoostsAsync();
            var topBoosts = await _dexScreenerService.GetTopTokenBoostsAsync();

            var trendingInfo = new
            {
                latestBoosts = latestBoosts.Take(10).Select(b => new
                {
                    b.ChainId,
                    b.TokenAddress,
                    b.Description,
                    b.Amount,
                    b.TotalAmount,
                    b.Url
                }),
                topBoosts = topBoosts.Take(10).Select(b => new
                {
                    b.ChainId,
                    b.TokenAddress,
                    b.Description,
                    b.Amount,
                    b.TotalAmount,
                    b.Url
                })
            };

            return Ok(trendingInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching trending tokens");
            return StatusCode(500, new { error = "Internal server error", message = ex.Message });
        }
    }
}
