using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Refit;
using TokenBot.Services;

namespace TokenBot.Tests;

/// <summary>
/// Simple integration tests for DexScreener API
/// Note: These tests make real API calls and are subject to rate limits
/// </summary>
public class DexScreenerApiTests
{
    private readonly IServiceProvider _serviceProvider;

    public DexScreenerApiTests()
    {
        var services = new ServiceCollection();
        
        // Configure logging
        services.AddLogging(builder => builder.AddConsole());
        
        // Add DexScreener API client
        services.AddRefitClient<IDexScreenerApi>()
            .ConfigureHttpClient(c =>
            {
                c.BaseAddress = new Uri("https://api.dexscreener.com");
                c.DefaultRequestHeaders.Add("User-Agent", "TokenBot/1.0");
            });
        
        // Add DexScreener service
        services.AddScoped<DexScreenerService>();
        
        _serviceProvider = services.BuildServiceProvider();
    }

    /// <summary>
    /// Test searching for SOL pairs
    /// </summary>
    public async Task TestSearchSolPairs()
    {
        var service = _serviceProvider.GetRequiredService<DexScreenerService>();
        var logger = _serviceProvider.GetRequiredService<ILogger<DexScreenerApiTests>>();
        
        try
        {
            logger.LogInformation("Testing search for SOL pairs...");
            
            var result = await service.SearchPairsAsync("SOL");
            
            if (result?.Pairs != null && result.Pairs.Any())
            {
                logger.LogInformation("✅ Search test passed! Found {Count} pairs", result.Pairs.Count);
                
                var solPair = result.Pairs.FirstOrDefault(p => 
                    p.BaseToken?.Symbol?.ToUpper() == "SOL" || 
                    p.QuoteToken?.Symbol?.ToUpper() == "SOL");
                
                if (solPair != null)
                {
                    logger.LogInformation("Found SOL pair: {PairAddress} on {ChainId}", 
                        solPair.PairAddress, solPair.ChainId);
                    logger.LogInformation("Price USD: {PriceUsd}, Liquidity: {Liquidity}", 
                        solPair.PriceUsd, solPair.Liquidity?.Usd);
                }
            }
            else
            {
                logger.LogWarning("⚠️ No pairs found in search results");
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Search test failed");
            throw;
        }
    }

    /// <summary>
    /// Test getting latest token boosts
    /// </summary>
    public async Task TestGetLatestBoosts()
    {
        var service = _serviceProvider.GetRequiredService<DexScreenerService>();
        var logger = _serviceProvider.GetRequiredService<ILogger<DexScreenerApiTests>>();
        
        try
        {
            logger.LogInformation("Testing get latest token boosts...");
            
            var boosts = await service.GetLatestTokenBoostsAsync();
            
            logger.LogInformation("✅ Latest boosts test passed! Found {Count} boosts", boosts.Count);
            
            if (boosts.Any())
            {
                var firstBoost = boosts.First();
                logger.LogInformation("First boost: {TokenAddress} on {ChainId}, Amount: {Amount}", 
                    firstBoost.TokenAddress, firstBoost.ChainId, firstBoost.Amount);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ Latest boosts test failed");
            throw;
        }
    }

    /// <summary>
    /// Test getting token pairs for SOL
    /// </summary>
    public async Task TestGetSolTokenPairs()
    {
        var service = _serviceProvider.GetRequiredService<DexScreenerService>();
        var logger = _serviceProvider.GetRequiredService<ILogger<DexScreenerApiTests>>();
        
        try
        {
            logger.LogInformation("Testing get SOL token pairs...");
            
            // SOL token address on Solana
            var solAddress = "So11111111111111111111111111111111111111112";
            var pairs = await service.GetTokenPairsAsync("solana", solAddress);
            
            logger.LogInformation("✅ SOL token pairs test passed! Found {Count} pairs", pairs.Count);
            
            if (pairs.Any())
            {
                var topPair = pairs.OrderByDescending(p => p.Liquidity?.Usd ?? 0).First();
                logger.LogInformation("Top liquidity pair: {PairAddress}, Liquidity: {Liquidity} USD", 
                    topPair.PairAddress, topPair.Liquidity?.Usd);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "❌ SOL token pairs test failed");
            throw;
        }
    }

    /// <summary>
    /// Run all tests
    /// </summary>
    public async Task RunAllTests()
    {
        var logger = _serviceProvider.GetRequiredService<ILogger<DexScreenerApiTests>>();
        
        logger.LogInformation("🚀 Starting DexScreener API integration tests...");
        
        try
        {
            await TestSearchSolPairs();
            await Task.Delay(1000); // Rate limiting
            
            await TestGetLatestBoosts();
            await Task.Delay(1000); // Rate limiting
            
            await TestGetSolTokenPairs();
            
            logger.LogInformation("🎉 All tests completed successfully!");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "💥 Test suite failed");
            throw;
        }
    }
}

/// <summary>
/// Simple console program to run the tests
/// </summary>
public class Program
{
    public static async Task Main(string[] args)
    {
        var tests = new DexScreenerApiTests();
        await tests.RunAllTests();
    }
}
