@TokenBot_HostAddress = http://localhost:5151

### Example endpoints - Search for Solana tokens
GET {{TokenBot_HostAddress}}/api/example/solana-search?query=SOL
Accept: application/json

###

### Example endpoints - Get popular Solana tokens
GET {{TokenBot_HostAddress}}/api/example/popular-solana-tokens
Accept: application/json

###

### Example endpoints - Get trending tokens
GET {{TokenBot_HostAddress}}/api/example/trending
Accept: application/json

###

### DexScreener API - Search pairs
GET {{TokenBot_HostAddress}}/api/dexscreener/search?query=SOL/USDC
Accept: application/json

###

### DexScreener API - Get latest token profiles
GET {{TokenBot_HostAddress}}/api/dexscreener/token-profiles/latest
Accept: application/json

###

### DexScreener API - Get latest token boosts
GET {{TokenBot_HostAddress}}/api/dexscreener/token-boosts/latest
Accept: application/json

###

### DexScreener API - Get top token boosts
GET {{TokenBot_HostAddress}}/api/dexscreener/token-boosts/top
Accept: application/json

###

### DexScreener API - Get SOL token pairs
GET {{TokenBot_HostAddress}}/api/dexscreener/token-pairs/solana/So11111111111111111111111111111111111111112
Accept: application/json

###

### DexScreener API - Get multiple tokens (SOL, USDC, USDT)
GET {{TokenBot_HostAddress}}/api/dexscreener/tokens/solana/So11111111111111111111111111111111111111112,EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v,Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB
Accept: application/json

###

### DexScreener API - Get specific pair (Jupiter SOL/USDC)
GET {{TokenBot_HostAddress}}/api/dexscreener/pairs/solana/JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN
Accept: application/json

###
